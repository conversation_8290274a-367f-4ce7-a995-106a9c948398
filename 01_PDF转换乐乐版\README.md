# 扫描王全能宝 - 微信小程序

这是一个仿照"扫描王全能宝"应用设计的微信小程序项目。

## 项目结构

```
├── project.config.json   # 项目配置文件
├── miniprogram/         # 小程序源码文件夹
│   ├── app.js          # 小程序入口文件
│   ├── app.json        # 小程序全局配置
│   ├── app.wxss        # 小程序全局样式
│   ├── sitemap.json    # 站点地图配置
│   ├── pages/          # 页面文件夹
│   │   └── index/      # 首页
│   │       ├── index.js     # 页面逻辑
│   │       ├── index.json   # 页面配置
│   │       ├── index.wxml   # 页面结构
│   │       └── index.wxss   # 页面样式
│   └── images/         # 图片资源文件夹
│       └── README.md   # 图片资源说明
└── README.md           # 项目说明
```

## 功能特点

### 主要功能
1. **文件扫描** - 快速生成PDF扫描件
2. **证件扫描** - 生成1:1电子复印件  
3. **文字识别** - 提取文字，生成Word文档
4. **表格识别** - 生成Excel文档

### 工具功能
- 图片转PDF
- 批量扫描
- 证件照制作
- 拍证者
- 老照片修复
- 手写识别
- 拍照翻译
- 批量识别
- PDF工具
- PDF水印、加密

### VIP会员功能
- 一次付费永享18+特权
- 会员权益展示

## 设计特色

1. **渐变背景** - 使用蓝色渐变背景营造专业感
2. **卡片式布局** - 主功能使用大卡片展示，次要功能使用小图标
3. **色彩搭配** - 橙色、蓝色、绿色等鲜明色彩区分不同功能
4. **VIP横幅** - 金色渐变背景突出会员特权

## 开发说明

### 运行环境
- 微信开发者工具
- 微信小程序基础库 2.0+

### 开发步骤
1. 在微信开发者工具中导入项目
2. 添加所需的图片资源到 `images/` 文件夹
3. 根据实际需求实现各功能的具体逻辑
4. 配置后端API接口（如需要）

### 图片资源
请参考 `miniprogram/images/README.md` 文件添加所需的图标资源。

### 项目导入
1. 打开微信开发者工具
2. 选择"导入项目"
3. 选择项目根目录（包含project.config.json的文件夹）
4. 项目会自动识别miniprogram文件夹作为源码目录

### 功能扩展
当前版本提供了基础的页面结构和样式，各功能按钮点击后会显示提示信息。
可以根据实际需求：
1. 集成相机API实现扫描功能
2. 集成OCR API实现文字识别
3. 添加文档管理页面
4. 实现VIP会员系统
5. 添加用户登录和数据同步

## 注意事项

1. 图片资源需要自行添加
2. 实际功能需要集成相应的API服务
3. 发布前需要配置小程序的AppID
4. 涉及相机、相册等权限需要在app.json中声明
