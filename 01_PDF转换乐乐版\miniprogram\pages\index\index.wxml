<!--index.wxml-->
<view class="container">
  <!-- VIP会员横幅 -->
  <view class="vip-banner">
    <view class="vip-content">
      <view class="vip-text">
        <text class="vip-title">一次付费永享</text>
        <text class="vip-highlight">18+</text>
        <text class="vip-title">特权</text>
      </view>
      <button class="vip-btn">立即抢购</button>
    </view>
    <view class="vip-badge">
      <text class="vip-icon">👑</text>
      <text class="vip-label">会员权益</text>
    </view>
    <view class="vip-card">VIP</view>
  </view>

  <!-- 主要功能区域 -->
  <view class="main-functions">
    <!-- 第一行：文件扫描和证件扫描 -->
    <view class="function-row">
      <view class="function-card orange" bindtap="onDocumentScan">
        <text class="function-icon">📄</text>
        <view class="function-title">文件扫描</view>
        <view class="function-desc">快速生成PDF扫描件</view>
      </view>
      <view class="function-card blue" bindtap="onIdCardScan">
        <text class="function-icon">🆔</text>
        <view class="function-title">证件扫描</view>
        <view class="function-desc">生成1:1电子复印件</view>
      </view>
    </view>

    <!-- 第二行：文字识别和表格识别 -->
    <view class="function-row">
      <view class="function-card blue-dark" bindtap="onTextRecognition">
        <text class="function-icon">📝</text>
        <view class="function-title">文字识别</view>
        <view class="function-desc">提取文字，生成Word</view>
      </view>
      <view class="function-card green" bindtap="onTableRecognition">
        <text class="function-icon">📊</text>
        <view class="function-title">表格识别</view>
        <view class="function-desc">生成Excel文档</view>
      </view>
    </view>
  </view>

  <!-- 工具栏 -->
  <view class="tools-section">
    <view class="tools-row">
      <view class="tool-item" bindtap="onImageToPdf">
        <text class="tool-icon">🖼️</text>
        <text class="tool-name">图片转PDF</text>
      </view>
      <view class="tool-item" bindtap="onBatchScan">
        <text class="tool-icon">📚</text>
        <text class="tool-name">批量扫描</text>
      </view>
      <view class="tool-item" bindtap="onIdPhoto">
        <text class="tool-icon">📷</text>
        <text class="tool-name">证件照</text>
      </view>
      <view class="tool-item" bindtap="onPhotoEdit">
        <text class="tool-icon">✂️</text>
        <text class="tool-name">拍证者</text>
      </view>
    </view>

    <view class="tools-row">
      <view class="tool-item" bindtap="onOldPhotoRepair">
        <text class="tool-icon">🔧</text>
        <text class="tool-name">老照片修复</text>
      </view>
      <view class="tool-item" bindtap="onHandwritingRecognition">
        <text class="tool-icon">✍️</text>
        <text class="tool-name">手写识别</text>
      </view>
      <view class="tool-item" bindtap="onPhotoTranslate">
        <text class="tool-icon">🌐</text>
        <text class="tool-name">拍照翻译</text>
      </view>
      <view class="tool-item" bindtap="onBatchRecognition">
        <text class="tool-icon">🔍</text>
        <text class="tool-name">批量识别</text>
      </view>
    </view>

    <view class="tools-row">
      <view class="tool-item" bindtap="onPdfTools">
        <text class="tool-icon">🛠️</text>
        <text class="tool-name">PDF工具</text>
      </view>
      <view class="tool-item" bindtap="onPdfWatermark">
        <text class="tool-icon">🔒</text>
        <text class="tool-name">PDF水印、加密</text>
      </view>
      <view class="tool-item" bindtap="onMore">
        <text class="tool-icon">⋯</text>
        <text class="tool-name">更多</text>
      </view>
      <view class="tool-item"></view> <!-- 占位 -->
    </view>
  </view>

  <!-- 我的文档 -->
  <view class="my-documents" bindtap="onMyDocuments">
    <view class="doc-header">
      <text class="folder-icon">📁</text>
      <text class="doc-title">我的文档</text>
    </view>
    <text class="arrow-icon">▶</text>
  </view>
</view>
