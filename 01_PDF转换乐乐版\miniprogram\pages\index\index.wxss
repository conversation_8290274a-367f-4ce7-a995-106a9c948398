/* index.wxss */
.container {
  padding: 20rpx;
  background: linear-gradient(180deg, #4A90E2 0%, #E8F4FD 100%);
  min-height: 100vh;
}

/* VIP会员横幅 */
.vip-banner {
  position: relative;
  background: linear-gradient(135deg, #FFD700 0%, #FFA500 100%);
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 40rpx;
  overflow: hidden;
}

.vip-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.vip-text {
  display: flex;
  align-items: baseline;
}

.vip-title {
  color: #8B4513;
  font-size: 28rpx;
  font-weight: 500;
}

.vip-highlight {
  color: #8B4513;
  font-size: 36rpx;
  font-weight: bold;
  margin: 0 8rpx;
}

.vip-btn {
  background: #8B4513;
  color: white;
  border-radius: 25rpx;
  padding: 12rpx 24rpx;
  font-size: 24rpx;
  border: none;
}

.vip-badge {
  position: absolute;
  top: 20rpx;
  right: 80rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.vip-icon {
  font-size: 40rpx;
  line-height: 40rpx;
}

.vip-label {
  color: #8B4513;
  font-size: 20rpx;
  margin-top: 8rpx;
}

.vip-card {
  position: absolute;
  top: -10rpx;
  right: 20rpx;
  background: #2C2C2C;
  color: #FFD700;
  padding: 8rpx 16rpx;
  border-radius: 8rpx;
  font-size: 24rpx;
  font-weight: bold;
  transform: rotate(15deg);
}

/* 主要功能区域 */
.main-functions {
  margin-bottom: 40rpx;
}

.function-row {
  display: flex;
  gap: 20rpx;
  margin-bottom: 20rpx;
}

.function-card {
  flex: 1;
  border-radius: 20rpx;
  padding: 40rpx 30rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  position: relative;
}

.function-card.orange {
  background: linear-gradient(135deg, #FF8C00 0%, #FF6347 100%);
}

.function-card.blue {
  background: linear-gradient(135deg, #4A90E2 0%, #357ABD 100%);
}

.function-card.blue-dark {
  background: linear-gradient(135deg, #1E3A8A 0%, #1E40AF 100%);
}

.function-card.green {
  background: linear-gradient(135deg, #10B981 0%, #059669 100%);
}

.function-icon {
  font-size: 80rpx;
  line-height: 80rpx;
  margin-bottom: 20rpx;
}

.function-title {
  color: white;
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.function-desc {
  color: rgba(255, 255, 255, 0.8);
  font-size: 24rpx;
}

/* 工具栏 */
.tools-section {
  background: white;
  border-radius: 20rpx;
  padding: 30rpx 20rpx;
  margin-bottom: 30rpx;
}

.tools-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 40rpx;
}

.tools-row:last-child {
  margin-bottom: 0;
}

.tool-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
  padding: 0 10rpx;
}

.tool-icon {
  font-size: 60rpx;
  line-height: 60rpx;
  margin-bottom: 15rpx;
}

.tool-name {
  color: #333;
  font-size: 22rpx;
  text-align: center;
  line-height: 1.2;
}

/* 我的文档 */
.my-documents {
  background: white;
  border-radius: 20rpx;
  padding: 30rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.doc-header {
  display: flex;
  align-items: center;
}

.folder-icon {
  font-size: 50rpx;
  line-height: 50rpx;
  margin-right: 20rpx;
}

.doc-title {
  color: #333;
  font-size: 32rpx;
  font-weight: 500;
}

.arrow-icon {
  font-size: 30rpx;
  line-height: 30rpx;
  color: #999;
}
