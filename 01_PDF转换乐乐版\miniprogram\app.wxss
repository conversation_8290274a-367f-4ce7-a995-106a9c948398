/**app.wxss**/
page {
  background-color: #f5f5f5;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Robot<PERSON>', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
}

.container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  box-sizing: border-box;
}

/* 通用按钮样式 */
.btn {
  border-radius: 25rpx;
  border: none;
  font-size: 28rpx;
  padding: 20rpx 40rpx;
  margin: 10rpx;
}

.btn-primary {
  background: linear-gradient(135deg, #4A90E2 0%, #357ABD 100%);
  color: white;
}

.btn-secondary {
  background: #f0f0f0;
  color: #333;
}

/* 通用卡片样式 */
.card {
  background: white;
  border-radius: 20rpx;
  padding: 30rpx;
  margin: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

/* 通用文本样式 */
.text-primary {
  color: #4A90E2;
}

.text-secondary {
  color: #666;
}

.text-muted {
  color: #999;
}

.text-center {
  text-align: center;
}

.text-bold {
  font-weight: bold;
}
